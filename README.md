# anki_guru

A new Flutter project.

## Getting Started

This project is a starting point for a Flutter application.

A few resources to get you started if this is your first Flutter project:

- [Lab: Write your first Flutter app](https://docs.flutter.dev/get-started/codelab)
- [Cookbook: Useful Flutter samples](https://docs.flutter.dev/cookbook)

For help getting started with Flutter development, view the
[online documentation](https://docs.flutter.dev/), which offers tutorials,
samples, guidance on mobile development, and a full API reference.

## Internationalization

This project uses GetX for internationalization. The translation files are located in the `lib/translations/jsons/` directory.

### Adding a new language

1. Create a new JSON file in the `lib/translations/jsons/` directory with the appropriate language code (e.g., `de.json` for German).
2. Add the language to the `languages` list in the `LocaleController` class in `lib/translations/app_translations.dart`.
3. Add the language to the `loadTranslations` method in the `TranslationLoader` class in `lib/translations/app_translations.dart`.

### Using translations in code

```dart
// Simple translation
Text('toolbox.common.submit'.tr)

// Translation with parameters
Text('app.greeting'.trParams({'name': '<PERSON>'}))
```

For more details, see the [MIGRATION_GUIDE.md](MIGRATION_GUIDE.md) file.

## Build
### Windows

设置代理： https://docs.flutter.cn/community/china

```bash
$env:PUB_HOSTED_URL="https://pub.flutter-io.cn"
$env:FLUTTER_STORAGE_BASE_URL="https://storage.flutter-io.cn"
$env:https_proxy="http://127.0.0.1:7897"
$env:SERIOUS_PYTHON_SITE_PACKAGES=$(pwd)/build/site-packages


rsync -avz --delete --dry-run --exclude-from='.rsync-exclude' -e "ssh -i /Users/<USER>/.ssh/id_ed25519" /Users/<USER>/code/anki-guru/ Thinkbook:"/C:/Users/<USER>/code/anki_guru/"

uv run python -m nuitka --mingw64 --module src --include-package=src --follow-import-to=src --remove-output 
mv src src2
uv run pyinstaller -w --collect-all=edge-tts,pdf2docx server.py
mv src2 src

flutter run -d win -v
flutter build windows --release -v
iscc .\inno_setup\setup.iss
```

### Mac

```bash
dart run serious_python:main package app/src -p Darwin --requirements -r,app/src/requirements.txt

# 编译动态链接库
env PYTHON_CONFIGURE_OPTS="--enable-framework" pyenv install 3.10.12

xcodebuild -create-xcframework -library '/Users/<USER>/Downloads/pdfium-ios-device-arm64 (2)/lib/libpdfium.dylib' -headers '/Users/<USER>/Downloads/pdfium-ios-device-arm64 (2)/include' -output ./pdfium.xcframework

# 同步模板
rsync -avP /Users/<USER>/code/Kevin-Anki-Templates/templates/Kevin* /Users/<USER>/code/anki-guru/third_party/templates/

uv run python -m nuitka --module src --include-package=src --follow-import-to=src --remove-output
mv src src2 && uv run pyinstaller -w --collect-all=edge-tts,pdf2docx server.py && mv src2 src
```

### Android
```bash

dart run serious_python:main package app/src -p Android --requirements -r,app/src/requirements.txt

export SERIOUS_PYTHON_SITE_PACKAGES=$(pwd)/build/site-packages 
flutter build apk --release --split-per-abi 
# with obfuscate
flutter build apk --obfuscate --split-debug-info="build/app/intermediates/symbols/" --release --split-per-abi

flutter build apk  --obfuscate --split-debug-info="build/app/intermediates/symbols/" --release --split-per-abi --target-platform=android-arm64

flutter_rust_bridge_codegen generate --watch
```

## Credits

- [serious-python](https://github.com/flet-dev/serious-python.git)
- [syncfusion_flutter_pdf ](https://help.syncfusion.com/flutter/pdf/getting-started)
- [genanki](https://github.com/kerrickstaley/genanki)
- [pyarmor](https://pyarmor.readthedocs.io/en/latest/tutorial/getting-started.html)
- [rinf](https://github.com/cunarist/rinf)

https://rydmike.com/flexcolorscheme/themesplayground-latest/

https://github.com/singerdmx/flutter-quill/
https://pub.dev/packages/flutter_keyboard_visibility
https://pub.dev/packages/flutter_tts
https://pub.dev/packages/in_app_purchase
https://pub.dev/packages/quick_actions
https://pub.dev/packages/google_mlkit_text_recognition
https://pub.dev/packages/external_app_launcher
https://pub.dev/packages/share_handler
https://pub.dev/packages/get_storage
https://github.com/swigger/wechat-ocr

https://pub.dev/packages/splash_master
https://pub.dev/packages/flutter_floating
https://pub.dev/packages/r_upgrade

https://github.com/jahnli/awesome-flutter-plugins?tab=readme-ov-file
https://pub.dev/packages/fluent_ui
https://pub.dev/packages/scribble
https://pub.dev/packages/image_painter
https://pub.dev/packages/quill_html_editor
https://github.com/MixinNetwork/flutter-plugins/blob/main/packages/desktop_webview_window/README.md
https://github.com/abner/flutter_js
https://github.com/ShayHill/docx2python
https://bigmodel.cn/dev/api/normal-model/glm-4v
https://github.com/KoljaB/RealtimeSTT/tree/master
https://pub.dev/packages/speech_to_text

https://wiki.loliot.net/docs/lang/flutter/engine/flutter-app-for-linux-arm64
https://github.com/ardera/flutter-engine-binaries-for-arm
https://pub.dev/packages/simple_chat
https://superwall.com/templates
https://pub.dev/packages/flutter_html
https://github.com/pan93412/yolo-rs/blob/master/src/lib.rs
https://github.com/leanflutter

## UI 
https://bdlukaa.github.io/fluent_ui/#/theming/typography
https://flutter-shadcn-ui.mariuti.com/components/tabs/

## 开发辅助工具
- https://github.com/yuaotian/go-cursor-help
```bash
curl -fsSL https://aizaozao.com/accelerate.php/https://raw.githubusercontent.com/yuaotian/go-cursor-help/refs/heads/master/scripts/run/cursor_mac_id_modifier.sh | sudo bash
```

- 移动端Anki模板调试：[Eruda](https://github.com/ankidroid/Anki-Android/wiki/Development-Guide#via-eruda-console-for-mobile-browsers)
```bash
<script src="https://cdn.jsdelivr.net/npm/eruda"></script>
<script>eruda.init();</script>
```

## OCR识别
https://github.com/csukuangfj/onnxruntime-libs/releases/tag/v1.22.0

## 制卡辅助工具

- Youtube视频下载：https://github.com/yt-dlp/yt-dlp

使用示例：
```bash
yt-dlp --proxy http://localhost:7897 --cookies-from-browser edge -f mp4 https://www.youtube.com/watch?v=187trX0mo1o&ab_channel=BoxofficeMovieScenes
```

- Youtube字幕下载：https://downsub.com/

- 片源网站：https://yts.mx/
- apkg文件结构：https://github.com/ankidroid/Anki-Android/wiki/Database-Structure

## Using Rust Inside Flutter

This project leverages Flutter for GUI and Rust for the backend logic,
utilizing the capabilities of the
[Rinf](https://pub.dev/packages/rinf) framework.

To run and build this app, you need to have
[Flutter SDK](https://docs.flutter.dev/get-started/install)
and [Rust toolchain](https://www.rust-lang.org/tools/install)
installed on your system.
You can check that your system is ready with the commands below.
Note that all the Flutter subcomponents should be installed.

```bash
rustc --version
flutter doctor
```

You also need to have the CLI tool for Rinf ready.

```bash
cargo install rinf
```

Messages sent between Dart and Rust are implemented using Protobuf.
If you have newly cloned the project repository
or made changes to the `.proto` files in the `./messages` directory,
run the following command:

```bash
rinf message
```

Now you can run and build this app just like any other Flutter projects.

```bash
flutter run
```

For detailed instructions on writing Rust and Flutter together,
please refer to Rinf's [documentation](https://rinf.cunarist.com).


