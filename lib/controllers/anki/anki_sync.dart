import 'dart:convert';
import 'dart:io';
import 'package:anki_guru/controllers/common.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:get/get.dart';
import 'package:permission_handler/permission_handler.dart';
import 'package:ulid/ulid.dart';

class AnkiUser {
  String id;
  String username;
  String password;
  DateTime createdAt;
  DateTime updatedAt;

  AnkiUser({
    String? id,
    required this.username,
    required this.password,
    DateTime? createdAt,
    DateTime? updatedAt,
  })  : id = id ?? Ulid().toString(),
        createdAt = createdAt ?? DateTime.now(),
        updatedAt = updatedAt ?? DateTime.now();

  /// Create a copy of this user with updated fields
  AnkiUser copyWith({
    String? id,
    String? username,
    String? password,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return AnkiUser(
      id: id ?? this.id,
      username: username ?? this.username,
      password: password ?? this.password,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? DateTime.now(),
    );
  }

  /// Convert to JSON for storage
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'username': username,
      'password': password,
      'createdAt': createdAt.toIso8601String(),
      'updatedAt': updatedAt.toIso8601String(),
    };
  }

  /// Create from JSON
  factory AnkiUser.fromJson(Map<String, dynamic> json) {
    return AnkiUser(
      id: json['id'],
      username: json['username'],
      password: json['password'],
      createdAt: DateTime.parse(json['createdAt']),
      updatedAt: DateTime.parse(json['updatedAt']),
    );
  }

  @override
  String toString() {
    return 'AnkiUser(id: $id, username: $username)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is AnkiUser && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;
}

class AnkiSyncController extends GetxController {
  // Observable variables for sync configuration
  final username = ''.obs;
  final password = ''.obs;
  final port = '8088'.obs;
  final dataDir = ''.obs;
  final maxPayload = '200'.obs;
  final host = '0.0.0.0'.obs;
  final ipAddresses = <String>[].obs;
  final showPassword = false.obs;
  final isServerRunning = false.obs;
  final pid = 0.obs;

  // User management variables
  final userList = <AnkiUser>[].obs;

  final _storage = StorageManager();
  final messageController = Get.find<MessageController>();

  // Add platform channel for native communication
  static const platform = MethodChannel('samples.flutter.dev/battery');

  @override
  void onInit() async {
    super.onInit();
    // Initialize controllers with current values
    await fetchIpAddresses();
    loadSettings();
  }

  // Method to simulate fetching IP addresses
  Future<void> fetchIpAddresses() async {
    final data = <String, dynamic>{};
    final resp = await messageController.request(data, "get_ip");
    if (resp.status == "success") {
      ipAddresses.value = List<String>.from(jsonDecode(resp.data));
    }
  }

  void loadSettings() {
    host.value =
        _storage.read(StorageBox.ankiSync, AnkiSyncKeys.host, "0.0.0.0");
    port.value = _storage.read(StorageBox.ankiSync, AnkiSyncKeys.port, "8088");
    dataDir.value =
        _storage.read(StorageBox.ankiSync, AnkiSyncKeys.dataDir, "");
    maxPayload.value =
        _storage.read(StorageBox.ankiSync, AnkiSyncKeys.maxPayload, "200");
    // Load user management data
    loadUsers();
  }

  // Method to save settings
  Future<void> saveSettings() async {
    // Update observable values from controllers
    if (host.value.isEmpty || port.value.isEmpty || dataDir.value.isEmpty) {
      showToastNotification(null, "anki.sync.configIncomplete".tr, "",
          type: "error");
      return;
    }
    // Implement the logic to save settings
    _storage.write(StorageBox.ankiSync, AnkiSyncKeys.host, host.value);
    _storage.write(StorageBox.ankiSync, AnkiSyncKeys.port, port.value);
    _storage.write(StorageBox.ankiSync, AnkiSyncKeys.dataDir, dataDir.value);
    _storage.write(
        StorageBox.ankiSync, AnkiSyncKeys.maxPayload, maxPayload.value);

    Get.showSnackbar(
      GetSnackBar(
        message: 'anki.sync.settingsSaved'.tr,
        duration: Duration(seconds: 2),
        margin: const EdgeInsets.all(16),
        borderRadius: 4,
      ),
    );
  }

  Future<bool> _checkAndRequestPermissions() async {
    if (Platform.isAndroid) {
      if (await Permission.notification.isDenied) {
        final status = await Permission.notification.request();
        return status.isGranted;
      }
      return true;
    }
    return true;
  }

  /// Start server with users (unified multi-user approach)
  /// Uses all available users for sync operations
  void startServer(BuildContext context, {List<AnkiUser>? users}) async {
    // 首先检查权限
    if (!await _checkAndRequestPermissions()) {
      Get.showSnackbar(
        GetSnackBar(
          message: 'anki.sync.needNotificationPermission'.tr,
          duration: Duration(seconds: 2),
          backgroundColor: Colors.red,
          margin: const EdgeInsets.all(16),
          borderRadius: 4,
        ),
      );
      return;
    }

    if (dataDir.value.isEmpty) {
      showToastNotification(null, "anki.sync.setDataLocation".tr, "",
          type: "error");
      return;
    }

    // Determine users to sync: provided list or all available users
    List<AnkiUser> usersToSync;
    if (users != null && users.isNotEmpty) {
      usersToSync = users;
    } else {
      // Use all available users
      if (userList.isEmpty) {
        showToastNotification(null, "anki.sync.user.noUsers".tr, "",
            type: "error");
        return;
      }
      usersToSync = userList.toList();
    }

    final progressController = Get.find<ProgressController>();
    progressController.reset(
      showOutputHint: false,
      numberButtons: 0,
    );
    progressController.showProgressDialog(context);
    progressController.updateProgress(
        status: "running", message: "anki.sync.startingServer".tr);

    try {
      // Start foreground service first
      if (Platform.isAndroid) {
        await platform.invokeMethod('startForegroundService');
      }

      // Convert users to multi-user format
      final usersData = usersToSync
          .map((user) => {
                "username": user.username,
                "password": user.password,
              })
          .toList();

      final data = {
        "server_path": "",
        "host": host.value,
        "port": port.value,
        "data_dir": dataDir.value,
        "max_payload": maxPayload.value,
        "users": usersData,
      };
      final resp = await messageController.request(data, "start_server");

      if (resp.status == "success") {
        isServerRunning.value = true;
        progressController.updateProgress(
            status: "completed",
            message: "anki.sync.serverStarted".tr,
            current: 100,
            total: 100);
      } else {
        if (Platform.isAndroid) {
          await platform.invokeMethod('stopForegroundService');
        }
        isServerRunning.value = false;
        progressController.updateProgress(
            status: "error",
            message: "anki.sync.startServerFailed"
                .trParams({"message": resp.message}));
      }
    } catch (e) {
      if (Platform.isAndroid) {
        await platform.invokeMethod('stopForegroundService');
      }
      logger.e('${"anki.sync.logStartServerError".tr}: $e');
      progressController.updateProgress(
          status: "error",
          message:
              "anki.sync.startServerError".trParams({"error": e.toString()}));
    }
  }

  void stopServer() async {
    try {
      final data = <String, dynamic>{};
      final resp = await messageController.request(data, "stop_server");

      if (resp.status == "success") {
        // Stop foreground service
        if (Platform.isAndroid) {
          await platform.invokeMethod('stopForegroundService');
        }
        isServerRunning.value = false;
        Get.showSnackbar(
          GetSnackBar(
            message: 'anki.sync.serverStopped'.tr,
            duration: Duration(seconds: 2),
            margin: const EdgeInsets.all(16),
            borderRadius: 4,
          ),
        );
      } else {
        Get.showSnackbar(
          GetSnackBar(
            message: 'anki.sync.stopServerFailed'.tr,
            duration: Duration(seconds: 2),
            margin: const EdgeInsets.all(16),
            borderRadius: 4,
          ),
        );
      }
    } catch (e) {
      logger.e('${"anki.sync.logStopServerError".tr}: $e');
      Get.showSnackbar(
        GetSnackBar(
          message: 'anki.sync.stopServerError'.tr,
          duration: Duration(seconds: 2),
          margin: const EdgeInsets.all(16),
          borderRadius: 4,
        ),
      );
    }
  }

  // User Management Methods

  /// Load users from storage
  void loadUsers() {
    try {
      final userListJson = _storage
          .read<List<dynamic>>(StorageBox.ankiSync, AnkiSyncKeys.userList, []);
      userList.value = userListJson
          .map((json) => AnkiUser.fromJson(Map<String, dynamic>.from(json)))
          .toList();
    } catch (e) {
      logger.e('Error loading users: $e');
      userList.value = [];
    }
  }

  /// Save users to storage
  void saveUsers() {
    try {
      final userListJson = userList.map((user) => user.toJson()).toList();
      _storage.write(StorageBox.ankiSync, AnkiSyncKeys.userList, userListJson);
    } catch (e) {
      logger.e('Error saving users: $e');
    }
  }

  /// Add a new user
  Future<bool> addUser(String username, String password) async {
    try {
      // Check if username already exists
      if (userList.any((user) => user.username == username)) {
        Get.showSnackbar(
          GetSnackBar(
            message: 'anki.sync.user.usernameExists'.tr,
            duration: Duration(seconds: 2),
            backgroundColor: Colors.red,
            margin: const EdgeInsets.all(16),
            borderRadius: 4,
          ),
        );
        return false;
      }

      final newUser = AnkiUser(
        username: username,
        password: password,
      );

      userList.add(newUser);
      saveUsers();

      Get.showSnackbar(
        GetSnackBar(
          message: 'anki.sync.user.addSuccess'.tr,
          duration: Duration(seconds: 2),
          margin: const EdgeInsets.all(16),
          borderRadius: 4,
        ),
      );
      return true;
    } catch (e) {
      logger.e('Error adding user: $e');
      Get.showSnackbar(
        GetSnackBar(
          message: 'anki.sync.user.addError'.tr,
          duration: Duration(seconds: 2),
          backgroundColor: Colors.red,
          margin: const EdgeInsets.all(16),
          borderRadius: 4,
        ),
      );
      return false;
    }
  }

  /// Update an existing user
  Future<bool> updateUser(
      String userId, String username, String password) async {
    try {
      final userIndex = userList.indexWhere((user) => user.id == userId);
      if (userIndex == -1) {
        Get.showSnackbar(
          GetSnackBar(
            message: 'anki.sync.user.notFound'.tr,
            duration: Duration(seconds: 2),
            backgroundColor: Colors.red,
            margin: const EdgeInsets.all(16),
            borderRadius: 4,
          ),
        );
        return false;
      }

      // Check if username already exists for other users
      if (userList
          .any((user) => user.username == username && user.id != userId)) {
        Get.showSnackbar(
          GetSnackBar(
            message: 'anki.sync.user.usernameExists'.tr,
            duration: Duration(seconds: 2),
            backgroundColor: Colors.red,
            margin: const EdgeInsets.all(16),
            borderRadius: 4,
          ),
        );
        return false;
      }

      final updatedUser = userList[userIndex].copyWith(
        username: username,
        password: password,
      );

      userList[userIndex] = updatedUser;
      saveUsers();

      Get.showSnackbar(
        GetSnackBar(
          message: 'anki.sync.user.updateSuccess'.tr,
          duration: Duration(seconds: 2),
          margin: const EdgeInsets.all(16),
          borderRadius: 4,
        ),
      );
      return true;
    } catch (e) {
      logger.e('Error updating user: $e');
      Get.showSnackbar(
        GetSnackBar(
          message: 'anki.sync.user.updateError'.tr,
          duration: Duration(seconds: 2),
          backgroundColor: Colors.red,
          margin: const EdgeInsets.all(16),
          borderRadius: 4,
        ),
      );
      return false;
    }
  }

  /// Delete a user
  Future<bool> deleteUser(String userId) async {
    try {
      final userIndex = userList.indexWhere((user) => user.id == userId);
      if (userIndex == -1) {
        Get.showSnackbar(
          GetSnackBar(
            message: 'anki.sync.user.notFound'.tr,
            duration: Duration(seconds: 2),
            backgroundColor: Colors.red,
            margin: const EdgeInsets.all(16),
            borderRadius: 4,
          ),
        );
        return false;
      }

      userList.removeAt(userIndex);
      saveUsers();

      Get.showSnackbar(
        GetSnackBar(
          message: 'anki.sync.user.deleteSuccess'.tr,
          duration: Duration(seconds: 2),
          margin: const EdgeInsets.all(16),
          borderRadius: 4,
        ),
      );
      return true;
    } catch (e) {
      logger.e('Error deleting user: $e');
      Get.showSnackbar(
        GetSnackBar(
          message: 'anki.sync.user.deleteError'.tr,
          duration: Duration(seconds: 2),
          backgroundColor: Colors.red,
          margin: const EdgeInsets.all(16),
          borderRadius: 4,
        ),
      );
      return false;
    }
  }
}
