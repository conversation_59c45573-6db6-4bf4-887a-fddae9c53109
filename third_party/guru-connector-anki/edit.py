import json
import re

import aqt
import aqt.browser.previewer
import aqt.editor
from anki.errors import NotFoundError
from aqt.qt import QCloseEvent, QMainWindow, Qt
from aqt.utils import restoreGeom, saveGeom, tooltip


def occlusions2mask_groups(occlusions: str):
    """Convert occlusion string format to mask groups array.

    Args:
        occlusions: String containing cloze occlusion data

    Returns:
        List of mask groups, where each group is a list of [left, top, width, height] coordinates
    """
    pattern = r"\{\{c(\d+)::image-occlusion:rect:left=([\d.]+):top=([\d.]+):width=([\d.]+):height=([\d.]+):oi=1\}\}"
    result = {}
    for item in re.finditer(pattern, occlusions):
        k, left, top, width, height = item.groups()
        if k not in result:
            result[k] = []
        result[k].append([float(left), float(top), float(width), float(height)])

    # Sort by cloze number to maintain consistent ordering
    sorted_keys = sorted(result.keys(), key=int)
    mask_groups = [result[k] for k in sorted_keys]
    return mask_groups


def validate_mask_groups(mask_groups):
    """Validate mask groups structure and clean invalid entries.

    Args:
        mask_groups: List of mask groups to validate

    Returns:
        Cleaned list of valid mask groups
    """
    if not isinstance(mask_groups, list):
        return []

    validated_groups = []
    for group in mask_groups:
        if isinstance(group, list):
            validated_masks = []
            for mask in group:
                if (isinstance(mask, list) and len(mask) >= 4 and
                    all(isinstance(coord, (int, float)) for coord in mask[:4])):
                    validated_masks.append(mask[:4])  # Only take first 4 coordinates
            if validated_masks:  # Only add non-empty groups
                validated_groups.append(validated_masks)

    return validated_groups


DOMAIN_PREFIX = "foosoft.ankiconnect."

# noinspection PyAttributeOutsideInit
class Edit(aqt.editcurrent.EditCurrent):
    dialog_geometry_tag = DOMAIN_PREFIX + "edit"
    dialog_registry_tag = DOMAIN_PREFIX + "Edit"
    dialog_search_tag = DOMAIN_PREFIX + "edit.history"

    def __init__(self, temp_note_id, origin_note_id):
        QMainWindow.__init__(self, None, Qt.WindowType.Window)
        self.note_id = temp_note_id
        self.origin_note_id = origin_note_id
        self.note = aqt.mw.col.get_note(temp_note_id)
        self.form = aqt.forms.editcurrent.Ui_Dialog()
        self.form.setupUi(self)
        self.setWindowTitle("Edit")
        self.setMinimumWidth(250)
        self.setMinimumHeight(400)
        restoreGeom(self, self.dialog_geometry_tag)

        self.form.buttonBox.setVisible(False)   # hides the Close button bar
        self.editor = aqt.editor.Editor(aqt.mw, self.form.fieldsArea, self)

        self.show()
        self.bring_to_foreground()
        self.show_note(self.note)

    def cleanup(self):
        self.editor.cleanup()
        saveGeom(self, self.dialog_geometry_tag)
        aqt.dialogs.markClosed(self.dialog_registry_tag)

        try:
            # Get the original note to preserve existing masks
            original_note = aqt.mw.col.get_note(self.origin_note_id)
            original_model = original_note.note_type()
            original_masks = []

            # Extract original masks from the "Masks" field with validation
            for info in original_model['flds']:
                if info['name'] == "Masks":
                    masks_field_content = original_note.fields[info['ord']]
                    if masks_field_content.strip():
                        try:
                            parsed_masks = json.loads(masks_field_content)
                            original_masks = validate_mask_groups(parsed_masks)
                        except json.JSONDecodeError as e:
                            print(f"Warning: Failed to parse original masks JSON: {e}")
                            original_masks = []
                    break

        # Get updated masks from the temporary note
        temp_note = aqt.mw.col.get_note(self.note_id)
        temp_model = temp_note.note_type()
        updated_mask_groups = []

        # Extract occlusion data from the "Text" field specifically
        for info in temp_model['flds']:
            if info['name'] == "Text":
                occlusion_field_content = temp_note.fields[info['ord']]
                if occlusion_field_content.strip():
                    updated_mask_groups = occlusions2mask_groups(occlusion_field_content)
                break

        # Intelligent merge strategy:
        # 1. If we have updated masks, use them (user made changes)
        # 2. If no updated masks but we had original masks, preserve originals
        # 3. If neither, create empty mask list
        if updated_mask_groups:
            final_mask_groups = updated_mask_groups
        elif original_masks:
            final_mask_groups = original_masks
        else:
            final_mask_groups = []

        # Remove the temporary note
        aqt.mw.col.remove_notes([self.note_id])

        # Update the original note with the final mask groups
        for info in original_model['flds']:
            if info['name'] == "Masks":
                original_note.fields[info['ord']] = json.dumps(final_mask_groups, ensure_ascii=False)
                break

        # Update browser selection and note
        (creator, instance) = aqt.dialogs._dialogs['Browser']
        if instance is None:
            return
        instance.table.clear_selection()
        aqt.mw.col.update_note(original_note)
        instance.table.select_single_card(original_note.cards()[0].id)

    def closeEvent(self, evt: QCloseEvent) -> None:
        self.editor.call_after_note_saved(self.cleanup)
    
    def bring_to_foreground(self):
        aqt.mw.app.processEvents()
        self.activateWindow()
        self.raise_()

    def show_note(self, note):
        cards = note.cards()
        self.editor.set_note(note)
        self.editor.card = cards[0] if cards else None

    def reload_notes_after_user_action_elsewhere(self):
        try:
            self.note.load()
        except NotFoundError:
            self.cleanup()
            return
        self.show_note(self.note)
